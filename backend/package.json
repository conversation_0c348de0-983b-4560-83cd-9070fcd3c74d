{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "@aws-sdk/client-s3": "^3.741.0", "@aws-sdk/lib-storage": "^3.741.0", "@aws-sdk/s3-request-presigner": "^3.806.0", "@bull-board/api": "^6.7.10", "@bull-board/express": "^6.7.10", "@google/generative-ai": "^0.19.0", "@modelcontextprotocol/sdk": "^1.12.1", "aws-sdk": "^2.1692.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "bull-board": "^1.7.2", "bullmq": "^5.41.7", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "express-validator": "^7.2.0", "fs": "^0.0.1-security", "google-auth-library": "^9.15.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mongoose": "^8.6.1", "multer": "^1.4.5-lts.1", "neo4j": "^2.0.0-RC2", "neo4j-driver": "^5.26.0", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "rate-limiter-flexible": "^5.0.5", "redis": "^4.7.0", "ua-parser-js": "^2.0.3", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/preset-env": "^7.24.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "jest": "^29.7.0", "nodemon": "^3.1.4"}, "type": "module", "jest": {"transform": {}, "testEnvironment": "node", "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}}}