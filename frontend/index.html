<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <!-- Favicon Configuration - Multiple formats for better compatibility -->
    <link rel="icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="icon" href="/icon-16x16.png" type="image/png" sizes="16x16" />
    <link rel="icon" href="/icon-32x32.png" type="image/png" sizes="32x32" />

    <!-- Apple Touch Icons for iOS devices -->
    <link rel="apple-touch-icon" href="/apple-touch-icon-180x180.png" sizes="180x180" />
    <link rel="apple-touch-icon" href="/icon-192x192.png" sizes="192x192" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/icon-192x192.png" />
    <meta name="msapplication-TileColor" content="#141f52" />
    <link rel="apple-touch-icon" href="/logo_accent.png" sizes="114x114" />
    <link rel="apple-touch-icon" href="/logo_accent.png" sizes="76x76" />
    <link rel="apple-touch-icon" href="/logo_accent.png" sizes="72x72" />
    <link rel="apple-touch-icon" href="/logo_accent.png" sizes="60x60" />
    <link rel="apple-touch-icon" href="/logo_accent.png" sizes="57x57" />

    <!-- Meta tags for better SEO and social sharing -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="AegisScholar - AI-powered teaching assistant for educators and students. Real-time performance analytics and personalized learning insights." />
    <meta name="keywords" content="AI, education, teaching assistant, student analytics, learning platform, educational technology" />
    <meta name="author" content="AegisScholar" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://aegisscholar.com/" />
    <meta property="og:title" content="AegisScholar - AI Teaching Assistant" />
    <meta property="og:description" content="Real-Time Performance Analytics. Gain immediate insights via your AI TA into student performance with analytics that pinpoint knowledge gaps and mastery areas." />
    <meta property="og:image" content="https://aegisscholar.com/icon-512x512.png" />
    <meta property="og:image:width" content="512" />
    <meta property="og:image:height" content="512" />
    <meta property="og:site_name" content="AegisScholar" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://aegisscholar.com/" />
    <meta property="twitter:title" content="AegisScholar - AI Teaching Assistant" />
    <meta property="twitter:description" content="Real-Time Performance Analytics. Gain immediate insights via your AI TA into student performance with analytics that pinpoint knowledge gaps and mastery areas." />
    <meta property="twitter:image" content="https://aegisscholar.com/icon-512x512.png" />

    <!-- Structured Data for Search Engines -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "AegisScholar",
      "description": "An AI-Native Teaching Assistant for Teachers and Students. Real-time performance analytics and personalized learning insights.",
      "url": "https://aegisscholar.com",
      "logo": "https://aegisscholar.com/icon-512x512.png",
      "image": "https://aegisscholar.com/icon-512x512.png",
      "applicationCategory": "EducationalApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "author": {
        "@type": "Organization",
        "name": "AegisScholar"
      }
    }
    </script>

    <!-- Google Sign-in -->
    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <meta name="google-signin-client_id" content="393244460263-41qr0r3ptggm2dafsink3kbk5tuvc5k1.apps.googleusercontent.com">
    <meta
      name="AegisScholar"
      content="An AI teaching assistant for Teachers and Students">
    <link rel="apple-touch-icon" href="/logo_accent.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="stylesheet" href="https://unpkg.com/katex@0.16.0/dist/katex.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Italianno&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Italianno&family=Playwrite+TZ:wght@100..400&display=swap" rel="stylesheet">
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>AegisScholar</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
