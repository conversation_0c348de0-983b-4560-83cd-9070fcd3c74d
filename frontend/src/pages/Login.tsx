import React, { useState, useEffect } from 'react';
import { login, googleRegister } from '../api';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowRightIcon, EnvelopeIcon, LockClosedIcon, EyeIcon, EyeSlashIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useUser } from '../contexts/userContext';
import { ToastContainer, toast } from 'react-toastify';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import ForgotPassword from '../components/ForgotPassword';
import { motion } from 'framer-motion';
import { GoogleLogin } from '@react-oauth/google';
import { useTheme } from '@/contexts/themeContext';

const Login = () => {
    const navigate = useNavigate();
    const location = useLocation();

    // Determine login type from query param
    const searchParams = new URLSearchParams(location.search);
    const loginType = searchParams.get('type'); // 'individual' or 'institution'
    const isIndividualLogin = loginType === 'individual';
    const showUserTypeSelection = loginType === 'institution';

    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [usertype, setUsertype] = useState('Teacher');
    const { user, setUser } = useUser();
    const [passwordType, setPasswordType] = useState('password');
    const [showForgotPassword, setShowForgotPassword] = useState(false);
    let from = location.state?.from?.pathname || '/aegis-ai';

    useEffect(() => {
        from = from || '/aegis-ai';
    }, [usertype]);

    const handleSubmit = async (e: any) => {
        e.preventDefault();
        const loginToast = toast.loading("Logging in...", {
            position: "top-center",
            toastId: 'login',
        });
        try {
            const data = await login(usertype, email, password);
            // Generate unique session ID for login tracking
            const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

            sessionStorage.setItem('email', email);
            sessionStorage.setItem('role', usertype);
            sessionStorage.setItem('sessionId', sessionId); // Track login session

            setUser({ ...user, accessToken: data.accessToken, email: email, role: usertype });
            console.error(`now user token is: ${data.accessToken}`);
            console.error(from);
            console.error(usertype);

            if (data.success) {
                toast.update(loginToast, {
                    render: "Login successful",
                    type: "success",
                    isLoading: false,
                    autoClose: 1500,
                });
                if (data.isFirstLogin) {
                    navigate('/welcome');
                } else {
                    setTimeout(() => navigate(from, { replace: true }), 1500);
                }
            }
        } catch (err) {
            setError('Invalid credentials');
            toast.update(loginToast, {
                render: "Login failed",
                type: "error",
                isLoading: false,
                autoClose: 1500,
            });
        }
    };

    const togglePass = () => {
        if (passwordType === 'password') {
            setPasswordType('text');
        } else {
            setPasswordType('password');
        }
    }

    const handleForgotPassword = () => {
        setShowForgotPassword(true);
    }

    const handleBackToLogin = () => {
        setShowForgotPassword(false);
    }

    // Google Sign-In success handler for login
    const handleGoogleSuccess = async (credentialResponse: any) => {
        const googleToast = toast.loading("Signing in with Google...", { position: "top-center" });
        try {
            const idToken = credentialResponse.credential;
            const res = await googleRegister(idToken, usertype);
            console.log('Google Sign-In response:', res);
            if (res.verified) {
                // Generate unique session ID for login tracking
                const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

                sessionStorage.setItem('email', res.email);
                sessionStorage.setItem('role', usertype);
                sessionStorage.setItem('sessionId', sessionId); // Track login session

                setUser({ ...user, accessToken: res.accessToken, email: res.email, role: usertype });
                toast.update(googleToast, {
                    render: "Google login successful",
                    type: "success",
                    isLoading: false,
                    autoClose: 1500,
                });
                if (res.isFirstLogin) {
                    navigate('/welcome', { replace: true });
                } else {
                    setTimeout(() => navigate('/aegis-ai', { replace: true }), 1500);
                }
            } else {
                setError('Google account not verified.');
                toast.update(googleToast, {
                    render: "Google account not verified.",
                    type: "error",
                    isLoading: false,
                    autoClose: 2000,
                });
            }
        } catch (err) {
            setError('Google login failed. Please try again.');
            toast.update(googleToast, {
                render: "Google login failed. Please try again.",
                type: "error",
                isLoading: false,
                autoClose: 2000,
            });
        }
    };

    // Google Sign-In failure handler for login
    const handleGoogleFailure = () => {
        setError('Google sign-in failed. Please try again.');
    };

    if (showForgotPassword) {
        return <ForgotPassword onBackToLogin={handleBackToLogin} usertype={usertype} />;
    }

    const theme = localStorage.getItem('theme') || 'light';
    return (
        <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ ease: 'easeIn', duration: 0.6 }}
            className="min-h-screen flex flex-col relative overflow-hidden bg-gradient-to-b from-background to-secondary/20"
        >
            <ToastContainer
            position="top-right"
            autoClose={2000}
            hideProgressBar={true}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme={theme === 'dark' ? 'dark' : 'light'}
            />

            {/* Main Content */}
            <div className="grow flex items-center justify-center p-4">
                <motion.div 
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="backdrop-blur-sm bg-card p-8 rounded-2xl border border-border w-full max-w-md shadow-lg"
                >
                    <div className="flex flex-col items-center mb-6">
                        <AegisScholarLogoWithoutText 
                            className="w-16 h-16" 
                            style={{ fill: 'var(--color-accent)' }}
                        />
                        <div className="mt-4 text-center">
                            <h1 className="text-3xl font-bold text-primary font-['Space_Grotesk'] mb-1">
                                Welcome Back
                            </h1>
                            <p className="text-foreground/70">Continue your learning journey</p>
                        </div>
                    </div>

                    {error && (
                        <motion.div 
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mb-6 p-3 rounded-lg bg-red-50 text-red-600 text-center"
                        >
                            {error}
                        </motion.div>
                    )}

                    {/* Temporarily commented out usertype selection - set to Teacher by default */}
                    {/* {showUserTypeSelection && (
                        <div className="flex justify-center space-x-4 mb-6">
                            <button
                                type="button"
                                onClick={() => setUsertype('Student')}
                                className={`px-6 py-2 rounded-full transition-all duration-300 cursor-pointer ${usertype === 'Student'
                                    ? 'bg-accent text-white shadow-md shadow-accent/20'
                                    : 'bg-muted/50 text-foreground hover:bg-muted/70'
                                }`}
                            >
                                Student
                            </button>
                            <button
                                type="button"
                                onClick={() => setUsertype('Teacher')}
                                className={`px-6 py-2 rounded-full transition-all duration-300 cursor-pointer ${usertype === 'Teacher'
                                    ? 'bg-accent text-white shadow-md shadow-accent/20'
                                    : 'bg-muted/50 text-foreground hover:bg-muted/70'
                                }`}
                            >
                                Teacher
                            </button>
                        </div>
                    )} */}

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                            <label className="block text-sm font-medium text-foreground/90 mb-2">
                                Username or Email
                            </label>
                            <div className="relative">
                                <input
                                    type="text"
                                    required
                                    className="w-full px-4 py-3 pl-10 bg-muted border border-border rounded-lg 
                                             focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                    placeholder="Enter your username or email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                                <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 w-[18px] h-[18px]" />
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-2">
                                <label className="text-sm font-medium text-foreground/90">
                                    Password
                                </label>
                            </div>
                            <div className="relative">
                                <input
                                    type={passwordType}
                                    required
                                    className="w-full px-4 py-3 pl-10 bg-muted border border-border rounded-lg 
                                             focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                    placeholder="Enter your password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                />
                                <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 w-[18px] h-[18px]" />
                                {(passwordType !== "password") ? 
                                    <EyeIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 cursor-pointer w-[18px] h-[18px]" onClick={togglePass} /> : 
                                    <EyeSlashIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 cursor-pointer w-[18px] h-[18px]" onClick={togglePass} />
                                }
                            </div>
                            <button 
                                type="button" 
                                onClick={handleForgotPassword} 
                                className="mt-2 text-sm text-accent hover:text-accent/80 hover:underline cursor-pointer"
                            >
                                Forgot password?
                            </button>
                        </div>

                        <button
                            type="submit"
                            className="w-full py-3 hover:cursor-pointer bg-accent text-white rounded-full hover:bg-accent/90 
                                     transition-all duration-300 flex items-center justify-center group shadow-md hover:shadow-lg hover:shadow-accent/20 hover:translate-y-[-2px]"
                        >
                            Sign In
                        </button>
                    </form>

                    {/* Google Sign-In Button - Only for individual login */}
                    {isIndividualLogin && (
                        <>
                            <div className="flex justify-center m-4">
                                <GoogleLogin
                                    onSuccess={handleGoogleSuccess}
                                    onError={handleGoogleFailure}
                                    useOneTap
                                />
                            </div>

                            <div className="relative my-6">
                                <div className="absolute inset-0 flex items-center">
                                    <div className="w-full border-t border-border"></div>
                                </div>
                                <div className="relative flex justify-center">
                                    <span className="bg-card px-4 text-sm text-foreground/60">or</span>
                                </div>
                            </div>
                        </>
                    )}

                    <p className="mt-6 text-center text-foreground/80">
                        Don't have an account?{' '}
                        <span
                            onClick={() => navigate(loginType ? `/register?type=${loginType}` : '/register')}
                            className="text-accent font-medium cursor-pointer hover:underline"
                        >
                            Sign up
                        </span>
                    </p>
                </motion.div>
            </div>

            {/* Footer */}
            <footer className="py-4 text-center text-foreground/60 text-sm">
                <div className="container mx-auto px-6">
                    © 2025 AegisScholar. All rights reserved.
                </div>
            </footer>
        </motion.div>
    );
};

export default Login;