import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';
import GradingSubmissions from '@/components/GradingSubmissions';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import ThemeToggle from '@/components/ThemeToggle';
import { useUser } from '../contexts/userContext';

const GradingSubmissionsPage: React.FC = () => {
    const navigate = useNavigate();
    const { user, setUser } = useUser();

    const handleLogout = () => {
        setUser(null);
        sessionStorage.clear();
        navigate('/login');
    };

    return (
        <div className="min-h-screen w-full bg-background p-4 pb-12 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                {/* Header */}
                <header className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <button
                            onClick={() => navigate('/aegis-grader')}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors"
                            title="Back to AegisGrader"
                        >
                            <ArrowLeftIcon className="w-4 h-4" />
                            <span className="hidden sm:inline">Back</span>
                        </button>
                        <div>
                            <h1 className="text-xl lg:text-2xl font-bold text-foreground">Grading Submissions</h1>
                        </div>
                    </div>
                    <div className="flex items-center gap-3">
                        <ThemeToggle size="default" />
                        <button
                            onClick={handleLogout}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors"
                            title="Logout"
                        >
                            <ArrowRightOnRectangleIcon className="w-4 h-4" />
                            <span className="hidden sm:inline">Logout</span>
                        </button>
                    </div>
                </header>

                {/* Main Content */}
                <div className="space-y-6">
                    <GradingSubmissions submissions={[]} />
                </div>
            </div>
        </div>
    );
};

export default GradingSubmissionsPage;
