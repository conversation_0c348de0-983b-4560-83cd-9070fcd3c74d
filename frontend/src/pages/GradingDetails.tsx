import React, { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { GradingStatus } from '../types/aegisGrader';
import { createPortal } from 'react-dom';
import {
    ArrowLeftIcon,
    DocumentTextIcon,
    ClockIcon,
    EyeIcon,
    TrophyIcon,
    ArrowDownTrayIcon,
    ChevronRightIcon,
    UsersIcon,
    ArrowTrendingUpIcon,
    MagnifyingGlassIcon,
    XMarkIcon,
    ShareIcon,
} from '@heroicons/react/24/outline';
import {
    CheckCircleIcon as CheckCircleSolid,
    StarIcon as StarSolid
} from '@heroicons/react/24/solid';
import { renderLatexContent } from '@/components/RenderLatexContent';

// --- Type Definitions ---
interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    children: React.ReactNode;
}

interface GradingResult {
    questionNumber: number;
    maxMarks: number;
    marksAwarded: number;
    feedback: string;
}

interface AnswerSheetResult {
    id: string;
    studentName: string;
    rollNumber: string;
    totalMarks: number;
    maxMarks: number;
    percentage: number;
    results: GradingResult[];
}

interface Section {
    name: string;
    section_marks: string;
    section_possible_marks: string;
    question: Question[] | Question;
}

interface Question {
    number: string;
    marks_awarded: string;
    marks_possible: string;
    feedback: string;
    marks_breakdown?: {
        criterion: Array<{
            _: string;
            name: string;
        }>;
    };
}

interface EvaluationResult {
    evaluation: {
        total_marks: string;
        maximum_possible_marks: string;
        percentage_score: string;
        section: Section[] | Section;
    };
}

interface AnswerSheetData {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl?: string;
    evaluationResult?: EvaluationResult;
}

interface SubmissionData {
    id: string;
    status: GradingStatus;
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
    questionPaper?: { pdfUrl?: string };
    rubric?: { pdfUrl?: string };
    gradingProgress?: number;
    answerSheets: AnswerSheetData[];
}

// --- Helper Functions ---
const formatScore = (score: number | string | undefined): string => {
    const num = typeof score === 'string' ? parseFloat(score) : score;
    if (num === undefined || isNaN(num)) return '-';
    return num % 1 === 0 ? num.toString() : num.toFixed(1);
};

const getScoreColorClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "text-muted-foreground";
    if (percentage >= 80) return "text-[hsl(var(--success))]";
    if (percentage >= 60) return "text-[hsl(var(--info))]";
    if (percentage >= 40) return "text-[hsl(var(--warning))]";
    return "text-[hsl(var(--danger))]";
};

const getScoreBgClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "bg-muted";
    if (percentage >= 80) return "bg-emerald-500";
    if (percentage >= 60) return "bg-sky-500";
    if (percentage >= 40) return "bg-amber-500";
    return "bg-red-500";
};

const getScoreBorderClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "border-muted";
    if (percentage >= 80) return "border-emerald-500";
    if (percentage >= 60) return "border-sky-500";
    if (percentage >= 40) return "border-amber-500";
    return "border-red-500";
};

const processLatexDelimiters = (text: string): string => {
    const parts = text.split(/(\$[^$]+\$)/g);
    return parts.map(part => {
        if (part.startsWith('$') && part.endsWith('$')) {
            return `$${part}$`;
        }
        return part;
    }).join('');
};

// --- Modal Component ---
const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
    useEffect(() => {
        const handleEsc = (e: KeyboardEvent) => {
            if (e.key === 'Escape') onClose();
        };
        if (isOpen) {
            document.addEventListener('keydown', handleEsc);
            document.body.style.overflow = 'hidden';
        }
        return () => {
            document.removeEventListener('keydown', handleEsc);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return createPortal(
        <div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 backdrop-blur-sm p-4 transition-opacity duration-300 ease-in-out"
            onClick={onClose}
        >
            <div
                className="bg-background rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto flex flex-col animate-fade-in-scale"
                onClick={e => e.stopPropagation()}
            >
                {children}
            </div>
            <style>{`
              @keyframes fade-in-scale {
                from { opacity: 0; transform: scale(0.95); }
                to { opacity: 1; transform: scale(1); }
              }
              .animate-fade-in-scale { animation: fade-in-scale 0.2s ease-out forwards; }
            `}</style>
        </div>,
        document.body
    );
};

// --- Stat Card Component ---
interface StatCardProps {
    label: string;
    value: string | number;
    unit?: string;
    colorClass?: string;
    icon?: React.ElementType;
}
const StatCard: React.FC<StatCardProps> = ({ label, value, unit = "", colorClass = "text-foreground", icon: Icon }) => (
    <div className="bg-background/60 dark:bg-background/30 p-4 rounded-lg border border-border/50 flex items-center space-x-3">
        {Icon && (
             <div className={`p-1.5 rounded-full ${colorClass.replace('text-', 'bg-')}/10 ${colorClass}`}>
                 <Icon className="h-5 w-5" />
             </div>
        )}
        <div>
            <p className="text-xs text-muted-foreground uppercase tracking-wider">{label}</p>
            <p className={`text-xl font-semibold ${colorClass}`}>
                {value}{unit}
            </p>
        </div>
    </div>
);

// --- Student List Item Component ---
interface StudentListItemProps {
    sheet: AnswerSheetData;
    onViewPdf: (url: string) => void;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (evaluationResult: EvaluationResult) => AnswerSheetResult;
}
const StudentListItem: React.FC<StudentListItemProps> = ({ sheet, onViewPdf, onViewResults, formatResults }) => {
    const percentage = sheet.evaluationResult ? parseFloat(sheet.evaluationResult.evaluation.percentage_score) : undefined;
    const totalMarks = sheet.evaluationResult ? parseFloat(sheet.evaluationResult.evaluation.total_marks) : undefined;
    const maxMarks = sheet.evaluationResult ? parseFloat(sheet.evaluationResult.evaluation.maximum_possible_marks) : undefined;
    const getInitials = (name: string) => name?.split(' ').map(n => n[0]).slice(0, 2).join('').toUpperCase() || '?';

    return (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 border border-border/50 rounded-lg hover:bg-muted hover:shadow-sm transition-all duration-200 space-y-3 sm:space-y-0">
            <div className="flex items-center gap-4 flex-1 min-w-0">
                 <div className={`w-11 h-11 rounded-full ${getScoreBgClass(percentage)}/10 flex items-center justify-center font-medium text-sm ${getScoreColorClass(percentage)} border ${getScoreBorderClass(percentage)}/30`}>
                    {getInitials(sheet.studentName)}
                </div>
                <div className="overflow-hidden">
                    <p className="font-medium truncate text-foreground">{sheet.studentName || "N/A"}</p>
                    <p className="text-xs text-muted-foreground truncate">
                        Roll No: {sheet.rollNumber || "N/A"}
                    </p>
                </div>
            </div>

            <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto justify-between">
                {sheet.evaluationResult ? (
                     <div className="text-right mr-2">
                        <p className={`text-lg font-semibold ${getScoreColorClass(percentage)}`}>
                            {formatScore(totalMarks)}/{formatScore(maxMarks)}
                        </p>
                        <p className={`text-xs font-medium ${getScoreColorClass(percentage)}`}>
                            ({formatScore(percentage)}%)
                        </p>
                    </div>
                ) : (
                    <div className="text-right mr-2">
                        <p className="text-sm font-medium text-muted-foreground">-</p>
                        <p className="text-xs text-muted-foreground">(Not Graded)</p>
                    </div>
                 )}

                <div className="flex gap-2 flex-shrink-0">
                    <button
                        title="View Answer Sheet PDF"
                        className="p-2 text-sm border border-border/50 rounded-md text-muted-foreground hover:bg-muted hover:text-foreground transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={() => sheet.pdfUrl && onViewPdf(sheet.pdfUrl)}
                        disabled={!sheet.pdfUrl}
                    >
                        <EyeIcon className="h-4 w-4" />
                    </button>
                    {sheet.evaluationResult && (
                        <button
                            title="View Detailed Results"
                            className="px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-1.5"
                            onClick={() => sheet.evaluationResult && onViewResults(formatResults(sheet.evaluationResult))}
                        >
                            Results <ChevronRightIcon className="h-3.5 w-3.5 opacity-70" />
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

// --- Main GradingDetails Component ---
export const GradingDetails: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { id: submissionId } = useParams<{ id: string }>();

    const [selectedResult, setSelectedResult] = useState<AnswerSheetResult | null>(null);
    const [sortBy, setSortBy] = useState<'name' | 'marks'>('marks');
    const [searchTerm, setSearchTerm] = useState('');

    const submission = useMemo(() => {
        const history = location.state?.testHistory as SubmissionData[] | undefined;
        return history?.find((sub: SubmissionData) => sub.id === submissionId);
    }, [location.state?.testHistory, submissionId]);

    const formatResults = useMemo(() => (evaluationResult: EvaluationResult): AnswerSheetResult => {
        const sections = evaluationResult.evaluation?.section
            ? Array.isArray(evaluationResult.evaluation.section)
                ? evaluationResult.evaluation.section
                : [evaluationResult.evaluation.section]
            : [];

        const questions: GradingResult[] = sections.flatMap(section => {
            const questionsInSection = section.question
                ? Array.isArray(section.question)
                    ? section.question
                    : [section.question]
                : [];

            return questionsInSection.map(q => ({
                questionNumber: parseFloat(q.number) || 0,
                maxMarks: parseFloat(q.marks_possible) || 0,
                marksAwarded: parseFloat(q.marks_awarded) || 0,
                feedback: q.feedback || ''
            }));
        });

        questions.sort((a, b) => a.questionNumber - b.questionNumber);

        const sheet = submission?.answerSheets.find(s => s.evaluationResult === evaluationResult);

        return {
            id: sheet?.id || submissionId || '',
            studentName: sheet?.studentName || 'Unknown Student',
            rollNumber: sheet?.rollNumber || 'N/A',
            totalMarks: parseFloat(evaluationResult.evaluation.total_marks) || 0,
            maxMarks: parseFloat(evaluationResult.evaluation.maximum_possible_marks) || 0,
            percentage: parseFloat(evaluationResult.evaluation.percentage_score) || 0,
            results: questions
        };
    }, [submission?.answerSheets, submissionId]);

    const classStats = useMemo(() => {
        const gradedSheets = submission?.answerSheets?.filter(sheet => sheet.evaluationResult);
        if (!gradedSheets || gradedSheets.length === 0) return null;

        const scores = gradedSheets.map(sheet =>
            parseFloat(sheet.evaluationResult!.evaluation.percentage_score) || 0
        );

        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const highest = Math.max(...scores);
        const lowest = Math.min(...scores);
        const passCount = scores.filter(score => score >= 40).length;
        const passPercentage = (passCount / scores.length) * 100;

        const distribution = {
            '80-100': scores.filter(s => s >= 80).length,
            '60-79': scores.filter(s => s >= 60 && s < 80).length,
            '40-59': scores.filter(s => s >= 40 && s < 60).length,
            '0-39': scores.filter(s => s < 40).length,
        };

        return {
            average,
            highest,
            lowest,
            passPercentage,
            totalStudents: gradedSheets.length,
            distribution
        };
    }, [submission?.answerSheets]);

    const filteredAndSortedSheets = useMemo(() => {
        if (!submission?.answerSheets) return [];

        return submission.answerSheets
            .filter(sheet =>
                (sheet.studentName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                (sheet.rollNumber?.toLowerCase() || '').includes(searchTerm.toLowerCase())
            )
            .sort((a, b) => {
                if (sortBy === 'name') {
                    return (a.studentName || '').localeCompare(b.studentName || '');
                } else {
                    const scoreA = a.evaluationResult ? parseFloat(a.evaluationResult.evaluation.total_marks) : -Infinity;
                    const scoreB = b.evaluationResult ? parseFloat(b.evaluationResult.evaluation.total_marks) : -Infinity;
                    return scoreB - scoreA;
                }
            });
    }, [submission?.answerSheets, searchTerm, sortBy]);

    const openPdf = (url: string | undefined) => {
        console.error('Opening PDF:', url);
        // if (!url) {
        //     alert('PDF URL is not available.');
        //     return;
        // }
        // try {
        //     const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
        //     if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        //         alert('Please allow popups for this website to view PDFs.');
        //     }
        // } catch (error) {
        //     console.error('Failed to open PDF:', error);
        //     alert('Unable to open PDF. Please check the URL or try again later.');
        // }
    };

    if (!submission) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-muted/40 text-muted-foreground">
                Submission data not found. <button onClick={() => navigate('/aegis-grader')} className="ml-2 text-primary hover:underline">Go Back</button>
            </div>
        );
    }

    const renderGradingResultsModal = () => {
        if (!selectedResult) return null;
        const percentage = selectedResult.percentage;

        return (
            <Modal isOpen={!!selectedResult} onClose={() => setSelectedResult(null)}>
                <div className="sticky top-0 bg-background/80 backdrop-blur-sm border-b border-border/50 px-6 py-4 flex justify-between items-center z-10">
                    <h2 className="text-xl font-semibold text-foreground">Detailed Evaluation</h2>
                    <button
                        onClick={() => setSelectedResult(null)}
                        className="p-1.5 text-muted-foreground hover:bg-muted rounded-full transition-colors"
                        aria-label="Close modal"
                    >
                        <XMarkIcon className="h-5 w-5" />
                    </button>
                </div>

                <div className="p-6 space-y-5">
                    <div className={`bg-gradient-to-r from-accent/30 to-transparent p-4 rounded-lg border-l-4 ${getScoreBorderClass(percentage)} flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 border border-border/50`}>
                        <div>
                            <h3 className="font-semibold text-lg text-foreground">{selectedResult.studentName}</h3>
                            <p className="text-sm text-muted-foreground">
                                Roll No: {selectedResult.rollNumber}
                            </p>
                        </div>
                        <div className={`text-right flex-shrink-0 sm:border-l border-border/50 sm:pl-4 sm:ml-4 mt-2 sm:mt-0`}>
                            <p className={`text-2xl font-bold ${getScoreColorClass(percentage)}`}>
                                {formatScore(selectedResult.totalMarks)} / {formatScore(selectedResult.maxMarks)}
                            </p>
                            <p className={`text-sm font-medium ${getScoreColorClass(percentage)}`}>
                                {formatScore(percentage)}%
                            </p>
                        </div>
                    </div>

                    <div className="space-y-4 pt-2">
                        <h4 className="text-base font-medium text-muted-foreground mb-3">Question Breakdown:</h4>
                        {selectedResult.results.map((result) => {
                            const questionPercentage = result.maxMarks > 0 ? (result.marksAwarded / result.maxMarks) * 100 : 0;
                            return (
                                <div key={result.questionNumber} className="pb-4 border-b border-border/30 last:border-b-0">
                                    <div className="flex justify-between items-center mb-2 gap-4">
                                        <p className="font-semibold text-foreground">Q{result.questionNumber}</p>
                                        <p className={`font-semibold whitespace-nowrap ${getScoreColorClass(questionPercentage)}`}>
                                            {formatScore(result.marksAwarded)} / {formatScore(result.maxMarks)}
                                        </p>
                                    </div>
                                    {result.feedback && (
                                        <div className="text-sm text-muted-foreground bg-background/50 dark:bg-background/20 border border-border/40 p-3 rounded-md mt-1">
                                            <p className="font-medium text-xs mb-1 text-foreground/80">Feedback:</p>
                                            <div className="prose prose-sm dark:prose-invert max-w-none">
                                                {renderLatexContent(processLatexDelimiters(result.feedback))}
                                            </div>
                                        </div>
                                    )}
                                    {!result.feedback && (
                                        <p className="text-xs text-muted-foreground/70 italic mt-1">No feedback provided.</p>
                                    )}
                                </div>
                            );
                        })}
                        {selectedResult.results.length === 0 && (
                            <p className="text-center text-muted-foreground italic py-4">No evaluation results found for this student.</p>
                        )}
                    </div>
                </div>

                 <div className="sticky bottom-0 bg-background/80 backdrop-blur-sm border-t border-border/50 px-6 py-4 flex justify-end gap-3 z-10">
                    <button
                        className="px-4 py-2 text-sm text-muted-foreground border border-border/60 rounded-md hover:bg-muted transition-colors"
                        onClick={() => setSelectedResult(null)}
                    >
                        Close
                    </button>
                    <button
                        className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-2"
                        onClick={() => alert("Download Report functionality to be implemented.")}
                    >
                        <ArrowDownTrayIcon className="h-4 w-4" />
                        Download Report
                    </button>
                </div>
            </Modal>
        );
    };

    return (
        <div className="min-h-screen pb-16 w-full bg-background p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                <header className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <button
                        onClick={() => navigate('/aegis-grader')}
                        className="flex items-center gap-2 px-3 py-1.5 text-sm text-muted-foreground hover:bg-muted rounded-md transition-colors"
                    >
                        <ArrowLeftIcon className="h-4 w-4" />
                        Back to Grader
                    </button>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1.5 ${submission.status === GradingStatus.COMPLETED
                        ? 'bg-[hsl(var(--success)/0.2)] text-[hsl(var(--success))] border border-[hsl(var(--success)/0.3)]'
                        : 'bg-[hsl(var(--info)/0.2)] text-[hsl(var(--info))] border border-[hsl(var(--info)/0.3)] animate-pulse'
                        }`}>
                        {submission.status === GradingStatus.COMPLETED ? (
                            <CheckCircleSolid className="h-4 w-4 text-[hsl(var(--success))]" />
                        ) : (
                            <ClockIcon className="h-4 w-4 text-[hsl(var(--info))]" />
                        )}
                        {submission.status}
                    </div>
                </header>

                <div className="bg-card rounded-xl shadow-sm border border-border/50 p-6">
                     <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                         <div className="flex-1">
                             <h1 className="text-2xl font-bold text-foreground mb-1">
                                {submission.testDetails.subject || 'Unnamed Test'}
                            </h1>
                            <p className="text-muted-foreground text-sm">
                                {submission.testDetails.className || 'N/A'} • {submission.testDetails.date || 'N/A'}
                            </p>
                         </div>
                         <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
                            <button
                                className="px-3 py-1.5 text-sm border border-border/60 rounded-md hover:bg-muted transition-colors flex items-center gap-2 text-muted-foreground disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => openPdf(submission.questionPaper?.pdfUrl)}
                                disabled={!submission.questionPaper?.pdfUrl}
                                title={submission.questionPaper?.pdfUrl ? "View Question Paper" : "Question Paper Not Available"}
                            >
                                <DocumentTextIcon className="h-4 w-4" />
                                Paper
                            </button>
                            <button
                                className="px-3 py-1.5 text-sm border border-border/60 rounded-md hover:bg-muted transition-colors flex items-center gap-2 text-muted-foreground disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => openPdf(submission.rubric?.pdfUrl)}
                                disabled={!submission.rubric?.pdfUrl}
                                title={submission.rubric?.pdfUrl ? "View Rubric" : "Rubric Not Available"}
                            >
                                <TrophyIcon className="h-4 w-4" />
                                Rubric
                            </button>
                        </div>
                     </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
                    <div className="lg:col-span-2 space-y-6">
                        {submission.status === GradingStatus.IN_PROGRESS && submission.gradingProgress !== undefined && (
                            <div className="bg-card rounded-xl shadow-sm border border-border/50 p-6">
                                <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-foreground">
                                    <ClockIcon className="h-5 w-5 text-primary" />
                                    Grading Progress
                                </h2>
                                <div className="space-y-3">
                                    <div className="relative h-2.5 w-full bg-muted rounded-full overflow-hidden">
                                        <div
                                            className="absolute top-0 left-0 h-full bg-gradient-to-r from-sky-400 to-sky-600 rounded-full transition-all duration-500 ease-out"
                                            style={{ width: `${submission.gradingProgress}%` }}
                                        />
                                    </div>
                                    <div className="flex justify-between text-xs text-muted-foreground">
                                        <p>{submission.gradingProgress}% Complete</p>
                                        <p>
                                            {submission.answerSheets.filter(s => s.evaluationResult).length} / {submission.answerSheets.length} Sheets Graded
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="bg-card rounded-xl shadow-sm border border-border/50 overflow-hidden">
                             <div className="p-6 border-b border-border/50">
                                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                    <h2 className="text-lg font-semibold flex items-center gap-2 text-foreground flex-shrink-0">
                                        <UsersIcon className="h-5 w-5 text-primary" />
                                        Student Submissions ({filteredAndSortedSheets.length})
                                    </h2>
                                    <div className="flex gap-2 w-full sm:w-auto">
                                         <div className="relative flex-grow sm:flex-grow-0">
                                             <MagnifyingGlassIcon className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                             <input
                                                type="text"
                                                placeholder="Search name/roll..."
                                                className="pl-8 py-1.5 text-sm border border-border/60 rounded-md w-full sm:w-40 focus:outline-none focus:ring-2 focus:ring-primary/30 bg-background/50"
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                             />
                                             {searchTerm && (
                                                <button
                                                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground p-0.5"
                                                    onClick={() => setSearchTerm('')}
                                                    aria-label="Clear search"
                                                >
                                                    <XMarkIcon className="h-3.5 w-3.5" />
                                                </button>
                                             )}
                                         </div>
                                         <select
                                            className="px-3 py-1.5 text-sm border border-border/60 rounded-md bg-background/50 text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 appearance-none"
                                            value={sortBy}
                                            onChange={(e) => setSortBy(e.target.value as 'name' | 'marks')}
                                         >
                                            <option value="marks">Sort: Marks</option>
                                            <option value="name">Sort: Name</option>
                                         </select>
                                    </div>
                                </div>
                             </div>
                             <div className="p-4 md:p-6 space-y-3 max-h-[600px] overflow-y-auto custom-scrollbar">
                                {filteredAndSortedSheets.length === 0 ? (
                                    <p className="text-center py-10 text-muted-foreground italic">
                                        {searchTerm ? "No students match your search." : "No answer sheets found for this submission."}
                                    </p>
                                ) : (
                                    filteredAndSortedSheets.map((sheet) => (
                                        <StudentListItem
                                            key={sheet.id}
                                            sheet={sheet}
                                            onViewPdf={openPdf}
                                            onViewResults={setSelectedResult}
                                            formatResults={formatResults}
                                        />
                                    ))
                                )}
                            </div>
                        </div>
                    </div>

                    <aside className="space-y-6 lg:space-y-8">
                        {classStats && (
                            <div className="bg-card rounded-xl shadow-sm border border-border/50 p-6">
                                <h2 className="text-lg font-semibold mb-5 flex items-center gap-2 text-foreground">
                                    <ArrowTrendingUpIcon className="h-5 w-5 text-primary" />
                                    Class Performance
                                </h2>
                                <div className="space-y-5">
                                    <div className="grid grid-cols-2 gap-4">
                                        <StatCard label="Average Score" value={classStats.average.toFixed(1)} unit="%" colorClass={getScoreColorClass(classStats.average)} />
                                        <StatCard label="Pass Rate" value={classStats.passPercentage.toFixed(1)} unit="%" colorClass={getScoreColorClass(classStats.passPercentage)} />
                                        <StatCard label="Highest Score" value={classStats.highest.toFixed(1)} unit="%" colorClass="text-[hsl(var(--success))]" />
                                        <StatCard label="Lowest Score" value={classStats.lowest.toFixed(1)} unit="%" colorClass={getScoreColorClass(classStats.lowest)} />
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2 text-muted-foreground">Grade Distribution ({classStats.totalStudents} Students)</h3>
                                        <div className="flex h-3 rounded-full overflow-hidden bg-muted">
                                            {[
                                                { range: '80-100', color: 'bg-emerald-500', count: classStats.distribution['80-100'] },
                                                { range: '60-79', color: 'bg-sky-500', count: classStats.distribution['60-79'] },
                                                { range: '40-59', color: 'bg-amber-500', count: classStats.distribution['40-59'] },
                                                { range: '0-39', color: 'bg-red-500', count: classStats.distribution['0-39'] }
                                            ].map((grade) => {
                                                const widthPercent = classStats.totalStudents > 0 ? (grade.count / classStats.totalStudents) * 100 : 0;
                                                return (
                                                    <div
                                                        key={grade.range}
                                                        className={`${grade.color} transition-all duration-500 ease-out`}
                                                        style={{ width: `${widthPercent}%` }}
                                                        title={`${grade.range}%: ${grade.count} student${grade.count !== 1 ? 's' : ''} (${widthPercent.toFixed(1)}%)`}
                                                    />
                                                );
                                            })}
                                        </div>
                                        <div className="flex justify-between text-xs text-muted-foreground mt-1.5 px-0.5">
                                            <span>Fail</span>
                                            <span>Pass</span>
                                            <span>Good</span>
                                            <span>Excellent</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {filteredAndSortedSheets.some(s => s.evaluationResult) && (
                            <div className="bg-card rounded-xl shadow-sm border border-border/50 p-6">
                                <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-foreground">
                                    <StarSolid className="h-5 w-5 text-yellow-500" />
                                    Top Performers
                                </h2>
                                <div className="space-y-3">
                                    {filteredAndSortedSheets
                                        .filter(s => s.evaluationResult)
                                        .slice(0, 3)
                                        .map((sheet, index) => {
                                            const percentage = parseFloat(sheet.evaluationResult!.evaluation.percentage_score);
                                            const rankColor = index === 0 ? 'bg-yellow-400 dark:bg-yellow-600' :
                                                              index === 1 ? 'bg-gray-300 dark:bg-gray-500' :
                                                                            'bg-orange-300 dark:bg-orange-600';
                                            const textColor = index === 0 ? 'text-yellow-900 dark:text-yellow-100' :
                                                              index === 1 ? 'text-gray-800 dark:text-gray-100' :
                                                                            'text-orange-900 dark:text-orange-100';

                                            return (
                                                <div key={sheet.id} className="flex items-center justify-between p-3 bg-background/60 dark:bg-background/30 rounded-lg border border-border/50">
                                                    <div className="flex items-center gap-3">
                                                        <div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold ${rankColor} ${textColor}`}>
                                                            {index + 1}
                                                        </div>
                                                        <p className="font-medium text-sm text-foreground truncate">{sheet.studentName}</p>
                                                    </div>
                                                    <p className={`font-bold text-sm ${getScoreColorClass(percentage)}`}>
                                                        {formatScore(percentage)}%
                                                    </p>
                                                </div>
                                            );
                                        })
                                    }
                                     {filteredAndSortedSheets.filter(s => s.evaluationResult).length === 0 && (
                                        <p className="text-sm text-muted-foreground italic text-center py-4">No graded students yet.</p>
                                     )}
                                </div>
                            </div>
                        )}

                         <div className="bg-card rounded-xl shadow-sm border border-border/50 p-6">
                             <h2 className="text-lg font-semibold mb-4 text-foreground">Actions</h2>
                             <div className="space-y-3">
                                <button
                                    className="w-full px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
                                    onClick={() => alert("Download All Results functionality to be implemented.")}
                                    disabled={!classStats}
                                >
                                    <ArrowDownTrayIcon className="h-4 w-4" />
                                    Download All Results
                                </button>
                                <button
                                    className="w-full px-4 py-2 text-sm border border-border/60 rounded-md text-muted-foreground hover:bg-muted transition-colors flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
                                    onClick={() => alert("Share with Admin functionality to be implemented.")}
                                    disabled={!classStats}
                                >
                                    <ShareIcon className="h-4 w-4" />
                                    Share with Admin
                                </button>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>

            {renderGradingResultsModal()}

            <style>{`
                .custom-scrollbar::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                }
                .custom-scrollbar::-webkit-scrollbar-track {
                    background: transparent;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb {
                    background: hsl(var(--border) / 0.5);
                    border-radius: 3px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                    background: hsl(var(--border));
                }
                .prose {
                    color: hsl(var(--muted-foreground));
                 }
                .dark .prose {
                   color: hsl(var(--muted-foreground));
                }
                .prose :where(code):not(:where([class~="not-prose"] *))::before,
                .prose :where(code):not(:where([class~="not-prose"] *))::after {
                    content: "";
                 }
            `}</style>
        </div>
    );
};

export default GradingDetails;