import { useState, useEffect } from 'react';
import RegisterTypeModal from '../components/RegisterTypeModal';
import LoginTypeModal from '../components/LoginTypeModal';
import { motion } from 'framer-motion';
import {
  Navbar,
  HeroSection,
  DashboardPreview,
  ProblemSection,
  SolutionSection,
  FeaturesSection,
  ImpactStatistics,
  HowItWorks,
  TestimonialsSection,
  SecuritySection,
  PricingSection,
  FAQSection,
  FinalCTA,
  ContactSection,
  Footer
} from '../components/homepage-sections';

const Home = () => {
  const [registerModalOpen, setRegisterModalOpen] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);

  // State for scroll-based scaling and navbar shrinking
  const [scrollY, setScrollY] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);

  // Scroll event handler
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);
      setIsScrolled(currentScrollY > 50); // Shrink navbar after 50px scroll
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ ease: 'easeIn', duration: 0.6 }}
      className="min-h-screen text-foreground bg-gradient-to-b from-background to-secondary/10 w-full overflow-x-hidden"
    >
      {/* Navbar */}
      <Navbar
        setRegisterModalOpen={setRegisterModalOpen}
        setLoginModalOpen={setLoginModalOpen}
        isScrolled={isScrolled}
        menuOpen={menuOpen}
        setMenuOpen={setMenuOpen}
        scrollY={scrollY}
      />

      {/* Main Content */}
      <main className="flex-grow overflow-hidden pt-10">
        {/* Hero Section */}
        <HeroSection
          setRegisterModalOpen={setRegisterModalOpen}
          setLoginModalOpen={setLoginModalOpen}
        />

        {/* Dashboard Preview */}
        <DashboardPreview
          scrollY={scrollY}
          isScrolled={isScrolled}
        />

        {/* Problem Section */}
        <ProblemSection />

        {/* Solution Section */}
        <SolutionSection />

        {/* Features Section */}
        <FeaturesSection />

        {/* Impact Statistics Section */}
        <ImpactStatistics />

        {/* How It Works Section */}
        <HowItWorks
          setRegisterModalOpen={setRegisterModalOpen}
          setLoginModalOpen={setLoginModalOpen}
        />

        {/* Testimonials Section */}
        <TestimonialsSection />

        {/* Security Section */}
        <SecuritySection />

        {/* Pricing Section */}
        <PricingSection
          setRegisterModalOpen={setRegisterModalOpen}
          setLoginModalOpen={setLoginModalOpen}
        />

        {/* FAQ Section */}
        <FAQSection />

        {/* Final CTA Section */}
        <FinalCTA
          setRegisterModalOpen={setRegisterModalOpen}
          setLoginModalOpen={setLoginModalOpen}
        />

        {/* Contact Section */}
        <ContactSection />
      </main>

      {/* Footer */}
      <Footer
        setRegisterModalOpen={setRegisterModalOpen}
        setLoginModalOpen={setLoginModalOpen}
      />

      {/* Modals */}
      <RegisterTypeModal open={registerModalOpen} onClose={() => setRegisterModalOpen(false)} />
      <LoginTypeModal open={loginModalOpen} onClose={() => setLoginModalOpen(false)} />
    </motion.div>
  );
};

export default Home;