import AegisScholarLogoWithoutText from '../../assets/AegisScholarLogoIcon';
import { BaseComponentProps, ModalHandlers } from './types';

interface FooterProps extends BaseComponentProps, ModalHandlers {}

const Footer: React.FC<FooterProps> = ({
  setLoginModalOpen,
  className = ''
}) => {
  return (
    <footer className={`bg-primary text-primary-foreground py-12 ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <AegisScholarLogoWithoutText
                className="w-8 h-8"
                style={{ fill: 'var(--color-primary-foreground)' }}
              />
              <span className="text-xl font-bold">AegisScholar</span>
            </div>
            <p className="text-primary-foreground/80 mb-4">
              Revolutionizing education through AI-powered adaptive learning.
            </p>
          </div>

          <div></div>
          
          {/* Product */}
          <div>
            <h4 className="text-lg font-bold mb-4">Product</h4>
            <ul className="space-y-2">
              <li><a href="#features" className="text-primary-foreground/80 hover:text-accent transition-colors">Features</a></li>
              <li><a href="#how-it-works" className="text-primary-foreground/80 hover:text-accent transition-colors">How It Works</a></li>
              <li><a href="#pricing" className="text-primary-foreground/80 hover:text-accent transition-colors">Pricing</a></li>
              <li><a href="#testimonials" className="text-primary-foreground/80 hover:text-accent transition-colors">Testimonials</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-bold mb-4">Support</h4>
            <ul className="space-y-2">
              <li><a href="#" className="text-primary-foreground/80 hover:text-accent transition-colors">Help Center</a></li>
              <li><button onClick={() => setLoginModalOpen(true)} className="text-primary-foreground/80 hover:text-accent transition-colors cursor-pointer">Login</button></li>
              <li><a href="/privacy-policy" className="text-primary-foreground/80 hover:text-accent transition-colors">Privacy Policy</a></li>
              <li><a href="#" className="text-primary-foreground/80 hover:text-accent transition-colors">Terms of Service</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-foreground/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-primary-foreground/60 text-sm mb-4 md:mb-0">
              © 2025 AegisScholar. All rights reserved.
            </div>
            <div className="flex items-center space-x-6 text-sm text-primary-foreground/60">
              <span>SOC 2 Compliant</span>
              <span>GDPR Ready</span>
              <span>99.9% Uptime</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
