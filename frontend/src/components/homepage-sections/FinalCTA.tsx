import { ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps, ModalHandlers } from './types';

interface FinalCTAProps extends BaseComponentProps, ModalHandlers {}

const FinalCTA: React.FC<FinalCTAProps> = ({
  setRegisterModalOpen,
  className = ''
}) => {
  return (
    <section className={`py-24 bg-gradient-to-r from-accent/10 via-primary/10 to-accent/10 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <h2 className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6">
            Unlock the educational growth you've been needing.
          </h2>
          <p className="text-xl text-foreground/70 mb-12 max-w-2xl mx-auto">
            Join thousands of educators who have transformed their teaching experience with AI-powered tools.
          </p>
          <div className="flex flex-col sm:flex-row justify-center items-center">
            <button
              onClick={() => setRegisterModalOpen(true)}
              className="bg-accent text-accent-foreground px-10 py-4 rounded-xl font-semibold text-lg transition-all hover:scale-105 inline-flex items-center gap-3"
            >
              Start Free Today
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FinalCTA;
