import React, { useCallback, useEffect, useState } from 'react';
import { GradingStatus, TestSubmission } from '../types/aegisGrader';
import { useNavigate } from 'react-router-dom';
import { axiosPrivate } from "@/axios"; // Assuming this is configured


// --- Helper Components --- (Using original naming if needed)
const StatusBadge: React.FC<{ status?: GradingStatus }> = React.memo(({ status = GradingStatus.PENDING }) => {
    const getStatusColor = useCallback(() => {
        switch (status) {
            case GradingStatus.COMPLETED: return 'bg-[hsl(var(--success)/0.2)] text-[hsl(var(--success))]';
            case GradingStatus.IN_PROGRESS: return 'bg-[hsl(var(--info)/0.2)] text-[hsl(var(--info))]';
            case GradingStatus.FAILED: return 'bg-[hsl(var(--danger)/0.2)] text-[hsl(var(--danger))]';
            default: return 'bg-[hsl(var(--muted-foreground)/0.2)] text-[hsl(var(--muted-foreground))]';
        }
    }, [status]);

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor()}`}>
            {status.toLowerCase()}
        </span>
    );
});

export const GradingSubmissions: React.FC<{ submissions: any[] }> = ({ submissions }) => {
    const navigate = useNavigate();
    const [testHistory, setTestHistory] = useState<TestSubmission[]>([]);
    const [error, setError] = useState<string>(""); // General error
    
    useEffect(() => {
            const fetchGradingSubmissions = async () => {
                try {
                    const response = await axiosPrivate.get('/api/aegisGrader/submissions');
                    console.error("Fetched submissions:", response.data);
                    setTestHistory(response.data.submissions);
                } catch (error) {
                    console.error("Error fetching submissions:", error);
                    setError("Failed to fetch submissions.");
                }
            }
            fetchGradingSubmissions();
        }, []);
    

    const viewGradingDetails = useCallback((id: string | undefined) => {
            // console.error("test history: ", testHistory);
            navigate(`/gradingDetails/` + id, {
                state: { testHistory },
            });
        }, [navigate, testHistory]);

    return (
        <div className="bg-card rounded-lg shadow-sm p-4 border border-border">
            <h2 className="text-lg font-semibold mb-4">Grading Submissions</h2>
            <div className="flex flex-col gap-4">
                {testHistory.length === 0 ? (
                    <div className="text-center text-gray-500">No tests submitted for grading yet</div>
                ) : (
                    testHistory.map((submission, index) => (
                        <div
                            key={index}
                            className="border rounded-lg p-4 hover:bg-muted cursor-pointer transition-colors"
                            onClick={() => viewGradingDetails(submission.id || '')}
                        >
                            <div className="flex justify-between items-start mb-2">
                                <div>
                                    <div className="flex items-center gap-2">
                                        <h3 className="font-medium">{submission.testDetails.subject}</h3>
                                        <StatusBadge status={submission.status || undefined} />
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                        {submission.testDetails.className} - {submission.testDetails.date}
                                    </p>
                                </div>
                            </div>
                            <div className="mt-2">
                                <p className="text-sm">Answer Sheets: {submission.answerSheets.length}</p>
                                {submission.status === GradingStatus.IN_PROGRESS && submission.gradingProgress != null && (
                                    <div className="mt-2">
                                        <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                                            <div
                                                className="h-full bg-primary rounded-full transition-all duration-500"
                                                style={{ width: `${submission.gradingProgress}%` }}
                                            />
                                        </div>
                                        <p className="text-sm text-muted-foreground mt-1">
                                            Grading Progress: {submission.gradingProgress}%
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
}

export default GradingSubmissions;